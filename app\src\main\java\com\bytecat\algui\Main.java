package com.bytecat.algui;
import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.view.Gravity;
import android.widget.LinearLayout;
import com.bytecat.algui.AlguiDemo.AlguiDemo;
import com.bytecat.algui.AlguiDemo.AlguiDemoESPDebugMenu;
import com.bytecat.algui.AlguiDemo.AlguiDemoESPMenu;
import com.bytecat.algui.AlguiDemo.AlguiDemoESPMenu2;
import com.bytecat.algui.AlguiDemo.AlguiDemoModMenu;
import com.bytecat.algui.AlguiDemo.AlguiDemoUnityESPMenu;
import com.bytecat.algui.AlguiHacker.AlguiCpp;
import com.bytecat.algui.AlguiHacker.AlguiMemTool;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiDocument;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiManager.AlguiObjectManager;
import com.bytecat.algui.AlguiTools.AlguiToolApp;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolNative;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiV;
import com.bytecat.algui.AlguiWindows.AlguiWin2FA;
import com.bytecat.algui.AlguiWindows.AlguiWinInform;
import com.bytecat.algui.AlguiWindows.AlguiWinMenu;
import com.topjohnwu.superuser.ipc.RootService;
import java.util.HashMap;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流QQ群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:35
 * @Describe
 */
//视频使用教学：【ByteCat404的个人空间-哔哩哔哩】 https://b23.tv/3u2y9YO
//文本教程见AlguiDemo.java文件
public class Main {

    //内存修改示例
    private void memHackDemo() {
        //HOOK需要在JNI实现，Java通过此方法控制JNI的HOOK功能
        //第一个参数为HOOK功能ID 第二个参数是这个HOOK功能的开关状态 
        //第三个参数(可选) 传入拖动条或输入框的数值使其能够自定义修改
        AlguiMemTool.JniHook(1, true, 0);

        //绘制示例查阅AlguiDemo.java文件

        //动态基址内存修改示例
        AlguiMemTool.setPackageName("com.yodo1.SkiSafari.yodo1");//设置包名
        long sAddr = AlguiMemTool.getModuleBaseAddr("libil2cpp.so:bss", AlguiMemTool.HEAD_CB);//获取模块基址 【模块名内使用[1]来代表获取第几个模块的基址 xa模块传HEAD_XA  cb模块传HEAD_CB  cd模块传HEAD_CD】
        long daddr = AlguiMemTool.jump64(AlguiMemTool.jump64(AlguiMemTool.jump64(sAddr + 0x88B8) + 0xA0) + 0x118) + 0x18;//跳转指针 跳到目标地址 【32位使用 jump32  64位使用jump64】
        //跳转指针如果不直观请使用下面这种方式
        /*
         long p1 = AlguiMemTool.jump64(sAddr + 0x88B8);
         long p2 = AlguiMemTool.jump64(p1 + 0xA0);
         long p3 = AlguiMemTool.jump64(p2 + 0x118);
         long addr = p3 + 0x18;
         */
        AlguiMemTool.setMemoryAddrValue("999999", daddr, AlguiMemTool.TYPE_DWORD, false);//修改目标值 【如果需要冻结将false改为true】

        //静态基址内存修改示例
        AlguiMemTool.setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        long jaddr = AlguiMemTool.getModuleBaseAddr("libgame.so", AlguiMemTool.HEAD_XA) + 0x376E10; //从模块基址偏移获取目标地址 【模块名内使用[1]来代表获取第几个模块的基址 xa模块传HEAD_XA  cb模块传HEAD_CB  cd模块传HEAD_CD】
        AlguiMemTool.setMemoryAddrValue("999999", jaddr, AlguiMemTool.TYPE_DWORD, false);//修改目标值 【如果需要冻结将false改为true】

        //特征码内存修改示例
        AlguiMemTool.clearResultList();//清空之前的搜索结果
        AlguiMemTool.setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
        AlguiMemTool.MemorySearch("120", AlguiMemTool.TYPE_DWORD);//内存搜索 【主特征码 支持范围，联合】
        AlguiMemTool.ImproveOffset("1", AlguiMemTool.TYPE_DWORD, -8);//偏移筛选特征码 【副特征码1 支持范围，联合】
        AlguiMemTool.ImproveOffset("12", AlguiMemTool.TYPE_DWORD, -4);//偏移筛选特征码 【副特征码2】
        //n个副特征码...
        AlguiMemTool.MemoryOffsetWrite("999999", AlguiMemTool.TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
        AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

        //范围搜索示例
        AlguiMemTool.clearResultList();//清空之前的搜索结果
        AlguiMemTool.setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
        AlguiMemTool.MemorySearch("1~20", AlguiMemTool.TYPE_DWORD);//内存搜索 【主特征码】将搜索出1~20之间的数据
        AlguiMemTool.ImproveOffset("1", AlguiMemTool.TYPE_DWORD, -8);//偏移筛选特征码 【副特征码1】
        AlguiMemTool.ImproveOffset("12", AlguiMemTool.TYPE_DWORD, -4);//偏移筛选特征码 【副特征码2】
        //n个副特征码...
        AlguiMemTool.MemoryOffsetWrite("999999", AlguiMemTool.TYPE_DWORD, 1, false);//筛选结果偏移修改 【如果需要冻结将false改为true】
        AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

        //联合搜索示例
        AlguiMemTool.clearResultList();//清空之前的搜索结果
        AlguiMemTool.setPackageName("com.fingersoft.hillclimb.noncmcc");//设置包名
        AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存
        AlguiMemTool.MemorySearch("1D;0.5F;27.1E:100", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序]
        //AlguiMemTool.MemorySearch("1D;0.5F;27.1E::100", AlguiMemTool.TYPE_DWORD);//联合内存搜索[按顺序]
        //AlguiMemTool.MemorySearch("1;5;27:100", AlguiMemTool.TYPE_DWORD);//联合内存搜索[值没有加类型符的情况将使用第二个参数作为默认类型]
        //AlguiMemTool.MemorySearch("1;5;27", AlguiMemTool.TYPE_DWORD);//联合内存搜索[不按顺序 没有范围的情况将使用默认范围500]
        //AlguiMemTool.MemorySearch("1;5;27::", AlguiMemTool.TYPE_DWORD);//联合内存搜索[按顺序 没有范围的情况将使用默认范围500]
        AlguiMemTool.MemorySearch("98;30~35;27~29::", AlguiMemTool.TYPE_DWORD);//联合搜索中也可加范围值
        //改善也和以上一样都支持 反正都和GG修改器相同 就不示例了
        AlguiMemTool.ImproveOffset("0.5", AlguiMemTool.TYPE_FLOAT, 0);//改善
        AlguiMemTool.MemoryOffsetWrite("999999", AlguiMemTool.TYPE_FLOAT, 0, false);//修改 【如果需要冻结将false改为true】
        AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果

        //其它拓展函数 [只展示实用性功能，其它功能自行在头文件查看]
        String value = AlguiMemTool.getMemoryAddrData(0x050A4AA0, AlguiMemTool.TYPE_FLOAT);//读取指定内存地址的指定类型的值
        AlguiMemTool.killProcess_Root("com.fingersoft.hillclimb.noncmcc"); // 杀掉指定包名的进程
        AlguiMemTool.stopProcess_Root("com.fingersoft.hillclimb.noncmcc"); // 暂停游戏
        AlguiMemTool.resumeProcess_Root("com.fingersoft.hillclimb.noncmcc"); // 恢复游戏
        AlguiMemTool.printResultListToFile("/data/user/0/com.bytecat.algui/cache/AlguiByteCat.log");//打印内存搜索筛选结果列表到文件 【传入文件绝对路径】
        AlguiMemTool.printFreezeListToFile("/data/user/0/com.bytecat.algui/cache/AlguiByteCat.log");//打印冻结列表到文件【传入文件绝对路径】
        //AlguiMemTool.setFreezeDelayMs(200);//设置冻结修改延迟【毫秒】
        AlguiMemTool.killAllInotify_Root(); //杀掉所有inotify监视器，防止游戏监视文件变化 【需要ROOT】
        AlguiMemTool.killGG_Root(); // 杀掉GG修改器 【需要ROOT】
        AlguiMemTool.killXscript_Root(); // 杀掉XS脚本 【需要ROOT】
        //.....

        //如果你需要执行外部编译好的c++可执行文件请使用以下方法
        AlguiCpp.exec(".so后缀的文件绝对路径");//普通执行
        AlguiCpp.exec_root(".so后缀的文件绝对路径");//root权限执行
        AlguiCpp.exec_lib(aContext, "在安装包lib文件夹中的.so后缀文件名称，确保lib文件夹每个子文件夹都存一份此文件");//普通执行
        AlguiCpp.exec_lib_root(aContext, "在安装包lib文件夹中的.so后缀文件名称，确保lib文件夹每个子文件夹都存一份此文件");//root执行
    }








    private static void main() {
        MyMenu("免费", "无限期", new HashMap<String, String>());
        //卡密验证  //‍🆘如果需要进行卡密验证请使用下面代码(去除/*和*/)，记得删除上面这一行代码
        //参数1是验证窗口显示在哪个活动中，参数2必须是主活动
        /*AlguiWin2FA.Get(aContext, AlguiActivity.MainActivity)
         .setCatTitleBackImage("MenuTopBack1.png")//背景图片
         //配置对接微验网络验证
         .setCatWYAppID("应用ID")//应用ID
         .setCatWYAppCode("版本号")//应用版本号(检测更新)
         .setCatWYOkCode(200)//成功状态码
         .setCatWYAppKey("appkey")//appkey密钥
         .setCatWYRC4_2("rc4-2")//rc4-2密钥
         .addRemoteFieldName("远程变量名1")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
         .addRemoteFieldName("远程变量名2")//添加一个远程变量名称 之后在MyMenu方法可以获取到它的值
         .startWY(new AlguiCallback.WY2FA(){
         //登录成功时执行 传递：卡密，到期时间，远程变量(如果有)
         public void success(String kami, String expireTime, HashMap<String, String> field) {
         MyMenu(kami, expireTime, field);
         }
         });*/
    }


    //你的菜单界面 如果启动了网络验证那么传递这些参数：卡密，到期时间，远程变量列表
    private static void MyMenu(String kami, String expireTime, final HashMap<String, String> field) {
        //加载动态库
        AlguiToolNative.loadLibrary("Algui");
        //🆘如果开发root插件请使用此代码绑定root权限 否则注释
        RootService.bind(new Intent(aContext, AlguiRootClient.class), new AlguiRootService());

        //如果有网络验证那么可以获取到这些东西
        String km =kami;//登录成功后的卡密
        String time =expireTime;//登录成功后的到期时间
        //获取一个远程变量的值 确保配置网络验证时添加这个远程变量的名称 参数：远程变量名称，获取失败时的默认值
        String value1=field.getOrDefault("远程变量名1", "这是远程变量获取失败时的默认值");
        String value2=field.getOrDefault("远程变量名2", "这是远程变量获取失败时的默认值");

        //获取机器码
        final String markcode =android.os.Build.FINGERPRINT;
        //检查并获取全网人数
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                String codeList = AlguiToolNetwork.get(AlguiDocument.getRead("codeList"));
                //AlguiLog.d("服务器测试，机器码列表",codeList);
                long count = 0;
                for (int i = 0; i < codeList.length(); i++) {
                    if (codeList.charAt(i) == ';') {
                        count++;
                    }
                }
                //机器码不存在时
                if (!codeList.contains(markcode)) {
                    AlguiToolNetwork.get(AlguiDocument.getAdd("codeList", markcode + ";"));
                    //AlguiLog.d("服务器测试，添加新用户",r);
                    count++;
                    return "欢迎新用户！你是第" + count + "个用户";
                }

                return "全网人数：" + count;
            }
            @Override
            protected void onPostExecute(String result) {
                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info_gta5);
                AlguiWinInform.Get(aContext) .showInfo_White(AlguiAssets.Icon.inform_info2, "欢迎", result, 6);
            }
        }.execute();



        //开始创建窗口UI (转到AlguiDemo.java或AlguiSDK.java查看更详细的UI教程)
        AlguiV a=AlguiV.Get(aContext);//获取UI快速构建器

        //绘制静态视图到屏幕上
        a.WinDraw
        (
            a.TextTag(null, "Algui专业版 联系QQ3353484607购买 [到期时间：%s]", 0xCE000000, expireTime)
            .setCatTextSize(8)
            .setCatTextColor(0xFFFFFFFF)
            ,//绘制的视图
            Gravity.BOTTOM | Gravity.START,//坐标原点 (这里右上原点)
            10, 10,//相对原点xy偏移
            false//视图是否可接收触摸事件
        );

        AlguiWinMenu menu = a.WinMenu("Algui专业版");//创建一个普通菜单窗口
        menu.setCatMenuSize(200, 330);//设置窗口大小
        menu.setCatMenuBufferLineMargins(3, 3, 3, 0);//设置菜单每行的外边距
        menu.setCatMenuBufferLineMaxView(1);//设置菜单一行最大可包含视图数量为1这样就不用手动换行了
        

        //往菜单添加一个可滚动的文本
        a.TextRoll(menu, "作者：ByteCat 作者QQ：3353484607 游戏逆向交流群730967224");

        a.DragFloat1(menu, "全局字体大小{v}", 5, 7, 50)
            .setCatIsUserUpdate(true)//只接受用户更新进度
            .setCatCallback(new AlguiCallback.DragBar(){
                public void start(double p) {
                    AlguiObjectManager.setAlguiTextSize((float)p);
                }
                public void update(double p) {
                    AlguiObjectManager.setAlguiTextSize((float)p);
                }
                public void end(double p) {
                    AlguiObjectManager.setAlguiTextSize((float)p);
                }
            }
        );

        //如果使用此方式设置进程后之后进行内存修改不要设置包名 否则失效
        a.FoldMenuSwitch(menu, "选择游戏进程[示例]", new AlguiCallback.Item(){
                public void item(int id) {

                    switch (id) {
                        default:
                        case 0://4399
                            AlguiMemTool.setPackageName("4399版本包名");
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                            AlguiWinInform.Get(aContext) .showInfo_White("https://pp.myapp.com/ma_icon/0/icon_196230_1734512088/256", "已切换进程", "当前为4399版本", 5);

                            break;
                        case 1://TapTap
                            AlguiMemTool.setPackageName("TapTap版本包名");
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                            AlguiWinInform.Get(aContext) .showInfo_White("https://android-artworks.25pp.com/fs08/2021/09/30/10/106_6277470a4726c83336916a66e77b068d_con_130x130.png", "已切换进程", "当前为TapTap版本", 5);

                            break;

                    }
                }
            }, "4399版本", "TapTap版本");

        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "游戏作弊功能[示例]")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(final boolean isSwitch) {

                    //我们在子线程运行内存修改 防止在UI线程卡顿或异常
                    new AsyncTask<Void, Void, String>() {
                        @Override
                        protected String doInBackground(Void... voids) {
                            if (isSwitch) {
                                AlguiMemTool.clearResultList();//清空之前的搜索结果
                                AlguiMemTool.setPackageName("com.JUG.lastfire");//设置包名
                                AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存范围
                                AlguiMemTool.MemorySearch("10", AlguiMemTool.TYPE_DWORD);//内存搜索
                                AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_FLOAT, 24);//改善结果
                                //如果要使用远程变量的值作为特效码像这样使用
                                //AlguiMemTool.ImproveOffset(field.getOrDefault("TestA", "这是远程变量获取失败时的默认值"), AlguiMemTool.TYPE_FLOAT, 24);//改善结果
                                AlguiMemTool.MemoryOffsetWrite("999999", AlguiMemTool.TYPE_FLOAT, 0, false);//修改 【如果需要冻结将false改为true】
                                AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                return "开启成功";
                            } else {
                                //功能关闭
                                AlguiMemTool.clearResultList();//清空之前的搜索结果
                                AlguiMemTool.setPackageName("com.JUG.lastfire");//设置包名
                                AlguiMemTool.setMemoryArea(AlguiMemTool.RANGE_ANONYMOUS);//设置内存范围
                                AlguiMemTool.MemorySearch("999999", AlguiMemTool.TYPE_DWORD);//内存搜索
                                AlguiMemTool.ImproveOffset("5", AlguiMemTool.TYPE_FLOAT, 24);//改善结果
                                AlguiMemTool.MemoryOffsetWrite("10", AlguiMemTool.TYPE_FLOAT, 0, false);//修改 【如果需要冻结将false改为true】
                                AlguiMemTool.clearResultList();//修改完成 则清空这次的搜索结果
                                return "关闭成功";
                            }
                        }
                        @Override
                        protected void onPostExecute(String result) {
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                            AlguiWinInform.Get(aContext) .showInfo_White(AlguiAssets.Icon.inform_success, "功能", result, 3);
                        }
                    }.execute();


                }
            }
        );



        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "Algui示例")
            .setCatSelect(true)//设置默认选中
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinMenu alguidemo  = AlguiDemo.show(aContext);
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        //开启
                        alguidemo.showMenu();
                    } else {
                        //关闭
                        alguidemo.hideWin();
                    }
                }
            }
        );

        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "日志窗口")
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        //开启
                        AlguiLog.getLogConsole(aContext).show();
                    } else {
                        //关闭
                        AlguiLog.getLogConsole(aContext).exit();
                    }
                }
            }
        );

        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "绘制调试示例")
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinMenu ESPdemo;
                public void click(boolean isSwitch) {
                    if (ESPdemo == null) 
                        ESPdemo = AlguiDemoESPDebugMenu.show(aContext);

                    if (isSwitch) {
                        //开启
                        ESPdemo.showMenu();
                    } else {
                        //关闭
                        ESPdemo.hideWin();
                    }
                }
            }
        );

        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "简单绘制示例")
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinMenu ESPdemo;
                public void click(boolean isSwitch) {
                    if (ESPdemo == null) 
                        ESPdemo = AlguiDemoESPMenu.show(aContext);

                    if (isSwitch) {
                        //开启
                        ESPdemo.showMenu();
                    } else {
                        //关闭
                        ESPdemo.hideWin();
                    }
                }
            }
        );

        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "复杂绘制示例")
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinMenu ESPdemo;
                public void click(boolean isSwitch) {
                    if (ESPdemo == null) 
                        ESPdemo = AlguiDemoESPMenu2.show(aContext);

                    if (isSwitch) {
                        //开启
                        ESPdemo.showMenu();
                    } else {
                        //关闭
                        ESPdemo.hideWin();
                    }
                }
            }
        );
        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "U3D绘制示例")
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinMenu ESPdemo;
                public void click(boolean isSwitch) {
                    if (ESPdemo == null) 
                        ESPdemo = AlguiDemoUnityESPMenu.show(aContext);

                    if (isSwitch) {
                        //开启
                        ESPdemo.showMenu();
                    } else {
                        //关闭
                        ESPdemo.hideWin();
                    }
                }
            }
        );
        //往菜单添加一个复选框并设置点击监听器
        a.CheckBox(menu, "作弊菜单示例")
            .setCatSelect(true)//设置默认选中
            .setCatCallback(new AlguiCallback.Click(){
                AlguiWinMenu modDemo=AlguiDemoModMenu.show(aContext);
                public void click(boolean isSwitch) {
                    if (isSwitch) {
                        //开启
                        modDemo.showMenu();
                    } else {
                        //关闭
                        modDemo.hideWin();
                    }
                }
            }
        );


    }








    /* Algui Main */  
    private Main() {  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  
    public static final String TAG = "Main";
    public static Context aContext;
    public static void start(Context c) {aContext = c; main();}


}
