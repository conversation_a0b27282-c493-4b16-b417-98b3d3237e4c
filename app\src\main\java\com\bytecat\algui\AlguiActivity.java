package com.bytecat.algui;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:32
 * @Describe 程序主入口
 */
import android.app.Activity;
import android.content.Intent;
import com.bytecat.algui.AlguiService;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiTools.AlguiToolPermission;

public class AlguiActivity {

    private AlguiActivity() {  
        /* cannot be instantiated */  
        throw new UnsupportedOperationException("cannot be instantiated");  
    }  

    public static final String TAG = "AlguiActivity";
    public static Activity MainActivity;//入口活动


    //true为活动窗口(免悬浮窗权限 但是仅显示这一个活动中)
    //false为后台全局窗口(要悬浮窗权限)
    //对于root插件必须改为false 否则无法全局悬浮
    private static boolean isActivityWindow = false;

    public static void start(final Activity c) {
        if (c != null) {
            MainActivity = c;
            AlguiLog.init(c);//初始化日志
            if (isActivityWindow) {
                Main.start(MainActivity);//单活动窗口直接进 (免悬浮窗权限)
            } else {
                //申请悬浮窗权限
                AlguiToolPermission.getWindow(c, new AlguiToolPermission.PermissionCallback() {
                        public void run(boolean b) {
                            if (b) {
                                //后台全局窗口则启动后台服务 (需悬浮窗权限)
                                MainActivity.startService(new Intent(MainActivity, AlguiService.class));
                            }else{
                                //一直不授予就进活动窗口
                                Main.start(MainActivity);
                            }
                        }
                    });
              
            }

        }
    }

}
