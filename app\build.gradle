plugins {
    id 'com.android.application'
}

android {
    compileSdk 33
    buildToolsVersion "33.0.0"

    defaultConfig {
        applicationId "com.bytecat.algui"
        minSdk 21
        targetSdk 26
        versionCode 130
        versionName "1.3.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    
    sourceSets {
        main {
            aidl.srcDirs = ['src/main/aidl']
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation 'androidx.annotation:annotation:1.7.0'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
}




