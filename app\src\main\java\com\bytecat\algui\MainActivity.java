package com.bytecat.algui;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import com.bytecat.algui.AlguiHacker.AlguiRootClient;
import com.bytecat.algui.AlguiHacker.AlguiRootService;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiManager.AlguiLog;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.topjohnwu.superuser.ipc.RootService;

public class MainActivity extends Activity {

    public static Activity main;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //setContentView(R.layout.activity_main);
        main = this;
        
        //一体直装直接启动
        //开发一体直装在游戏入口onCreate第一行添加下面代码(对应：AlguiActivity.start(this);)
        //invoke-static {p0}, Lcom/bytecat/algui/AlguiActivity;->start(Landroid/app/Activity;)V
        AlguiActivity.start(this);

        //外部Root辅助可以使用下面代码按钮启动()
        RootService.bind(new Intent(this, AlguiRootClient.class), new AlguiRootService());
        AlguiLinearLayout layout=new AlguiLinearLayout(this);
        layout.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.MATCH_PARENT);
        layout.setGravity(Gravity.CENTER);
        layout.setCatBackColor(0xff303030);
        setContentView(layout);

        new AlguiViewButton(this, "启动")
            .setCatPadding(7, 3, 7, 3)
            .setCatParentLayout(layout)
            .setCatTextSize(15)
            .setCatBackColor(0xff5A595B)
            .setCatTextColor(0xFFFFFFFF)
            .setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    AlguiActivity.start(main);
                }
            }
        );




    }


}
