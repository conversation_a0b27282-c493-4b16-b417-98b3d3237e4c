package com.bytecat.algui.AlguiWindows;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/12/29 17:47
 * @Describe 网络验证窗口
 */
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.AnimatedImageDrawable;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.icu.text.SimpleDateFormat;
import android.icu.util.GregorianCalendar;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.Gravity;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.Toast;
import com.bytecat.algui.AlguiManager.AlguiAssets;
import com.bytecat.algui.AlguiManager.AlguiCallback;
import com.bytecat.algui.AlguiTools.AlguiToolAudio;
import com.bytecat.algui.AlguiTools.AlguiToolCache;
import com.bytecat.algui.AlguiTools.AlguiToolImage;
import com.bytecat.algui.AlguiTools.AlguiToolNetwork;
import com.bytecat.algui.AlguiTools.AlguiToolPermission;
import com.bytecat.algui.AlguiTools.AlguiToolRC4;
import com.bytecat.algui.AlguiViews.AlguiLinearLayout;
import com.bytecat.algui.AlguiViews.AlguiViewButton;
import com.bytecat.algui.AlguiViews.AlguiViewInputBox;
import com.bytecat.algui.AlguiViews.AlguiViewText;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.json.JSONException;
import org.json.JSONObject;
import com.bytecat.algui.AlguiManager.AlguiLog;

public class AlguiWin2FA {

    public static final String TAG = "AlguiWin2FA";
    Context aContext;
    Activity aActivity;
    private static AlguiWin2FA obj;
    AlertDialog dialog;//对话框
    AlguiLinearLayout rootLayout;//根布局
    AlguiViewText title;//标题
    AlguiViewText text;//信息文本
    AlguiViewInputBox kamiInput;//输入框占位(防止不显示输入法)
    AlguiLinearLayout layout;//容器布局

    // 获取 dialog 对象的方法
    public AlertDialog getByteDialog() {
        return dialog; // 返回 AlertDialog 对象
    }

    // 获取 rootLayout 对象的方法
    public AlguiLinearLayout getByteRootLayout() {
        return rootLayout; // 返回 AlguiLinearLayout 对象
    }

    // 获取 title 对象的方法
    public AlguiViewText getByteTitle() {
        return title; // 返回 AlguiViewText 对象
    }

    // 获取 text 对象的方法
    public AlguiViewText getByteText() {
        return text; // 返回 AlguiViewText 对象
    }

    // 获取 kamiInput 对象的方法
    public AlguiViewInputBox getByteKamiInput() {
        return kamiInput; // 返回 AlguiViewInputBox 对象
    }

    // 获取 layout 对象的方法
    public AlguiLinearLayout getByteLayout() {
        return layout; // 返回 AlguiLinearLayout 对象
    }
    //单例访问器
    public static AlguiWin2FA Get(Context context, Activity activity) {
        if (obj == null) {
            obj = new AlguiWin2FA(context, activity);
        }
        return obj;
    }
    private AlguiWin2FA(Context context, Activity activity) {
        aContext = context;
        aActivity = activity;
        init();
    }
    private void init() {
        //根布局
        rootLayout = new AlguiLinearLayout(aContext);
        rootLayout.setCatSize(AlguiLinearLayout.LayoutParams.WRAP_CONTENT, AlguiLinearLayout.LayoutParams.WRAP_CONTENT);
        rootLayout.setOrientation(LinearLayout.VERTICAL);//垂直
        rootLayout.setGravity(Gravity.CENTER_HORIZONTAL);//横向居中
        rootLayout.setCatBackColor(0xBE000000);
        rootLayout.setCatRadiu(8);
        rootLayout.setCatPadding(0, 0, 0, 10);
        //标题
        title = new AlguiViewText(aContext, "服务器错误");
        title.setCatTextGravity(Gravity.CENTER);
        title.setCatTextSize(20);
        title.setCatTextColor(0xFFFFFFFF);
        title.setCatTextGlow(2, 0xFF000000);

        //信息
        text = new AlguiViewText(aContext);
        text.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.WRAP_CONTENT);
        rootLayout.setGravity(Gravity.CENTER);//居中
        text.setCatText(
            "我们未能成功验证您的请求，请尝试以下操作：" +
            "\n➤ 检查网络状态并确保连接稳定" +
            "\n➤ 在确保网络正常的情况下，请重启程序重新尝试进行验证" +
            "\n➤ 若您正在使用VPN或特殊的网络配置，建议关闭VPN后重试" +
            "若问题依旧，可能是服务商跑路或开发者网络验证配置未同步到本地！"
        );
        text.setCatTextIsLink(true);
        text.setCatTextSize(12);
        text.setCatMargins(20, 10, 20, 10);

        //输入框先占位 不然之后显示对话框后再次添加则获取不到输入法
        kamiInput = Input("请输入卡密", "");
        kamiInput.setVisibility(View.GONE);

        //容器布局
        layout = new AlguiLinearLayout(aContext);
        layout.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.MATCH_PARENT);
        layout.setOrientation(LinearLayout.VERTICAL);//垂直
        layout.setGravity(Gravity.CENTER_HORIZONTAL);//横向居中

        AlguiViewButton button =  Button("退出").setCatCallback(new AlguiCallback.Click(){
                public void click(boolean b) {
                    System.exit(0);
                }
            }
        );

        rootLayout.addView(title, text, kamiInput, layout, button);

        //对话框
        AlertDialog.Builder  builder = new AlertDialog.Builder(aContext);//构建器
        builder.setView(rootLayout);//为对话框设置自定义布局
        dialog = builder.create();//创建对话框
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));//设置对话框背景透明 (以便应用圆角)
        //设置窗口类型
        if (aContext instanceof Activity) {
            //对于上下文为活动时 窗口类型设置为应用级窗口 (无需悬浮窗权限)
            dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION);
        } else {
            //对于其它 则使用系统级后台全局窗口 (需要悬浮窗权限)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                //对于安卓8.0以上
                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
            } else {
                //对于安卓8.0以下
                dialog.getWindow().setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
            }
            //申请悬浮窗权限
            AlguiToolPermission.getWindow(aContext);
        }
        dialog.setCanceledOnTouchOutside(false); //禁止点击对话框外部关闭对话框
        //禁用返回键关闭对话框 
        dialog.setCancelable(false);
        setCatTitleBackImage(null);


    }




    //MD5哈希
    public static String encodeMD5(String str) {
        if (str == null) {
            return "null";
        }
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(str.getBytes("UTF-8"));
            byte messageDigest[] = md5.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                hexString.append(String.format("%02X", b));
            }
            return hexString.toString().toLowerCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }


    //设置标题背景图片
    public AlguiWin2FA setCatTitleBackImage(final String Url_Base64_FilePath) {
        if (Url_Base64_FilePath != null) {
            title.setCatPadding(20, 20, 20, 20);
            title.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, 70);
            //确保在布局计算完成后才设置
            title.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                    Drawable menuTopBackIMG;//菜单顶部布局背景图片
                    public void update() {
                        int layoutWidth = title.getWidth();
                        int layoutHeight = title.getHeight();

                        if (menuTopBackIMG != null) {
                            //如果是GIF图片直接设置
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P && menuTopBackIMG instanceof AnimatedImageDrawable) {
                                title.setBackground((AnimatedImageDrawable) menuTopBackIMG);
                            } else {
                                //对于需要毛玻璃模糊
                                //if (blurRadius > 0)
                                menuTopBackIMG = AlguiToolImage.psImageBlur(menuTopBackIMG, 55, aContext);
                                //对于非GIF图片进行缩放处理，图片宽超出布局宽则缩放到布局宽然后裁剪掉多余的高度部分
                                Bitmap originalBitmap = AlguiToolImage.drawableToBitmap(menuTopBackIMG);
                                int originalWidth = originalBitmap.getWidth();
                                int originalHeight = originalBitmap.getHeight();

                                // 图片与布局宽高一致直接设置
                                if (layoutWidth == originalWidth && originalHeight == layoutHeight) {
                                    title.setBackground(menuTopBackIMG);
                                } else {
                                    //否则将图片宽度缩放为与布局宽度一致然后裁剪掉高度多余的部分
                                    //计算缩放比例
                                    float scale = (float) layoutWidth / originalWidth;
                                    int scaledWidth = layoutWidth;
                                    int scaledHeight = (int) (originalHeight * scale);

                                    //缩放图片
                                    Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, scaledWidth, scaledHeight, true);

                                    //如果缩放后的高度超出布局高度则裁剪掉超出的高度部分
                                    if (scaledHeight > layoutHeight) {
                                        //计算裁剪区域
                                        int cropHeight = scaledHeight - layoutHeight;
                                        int cropStartY = cropHeight / 2;
                                        Bitmap croppedBitmap = Bitmap.createBitmap(scaledBitmap, 0, cropStartY, scaledWidth, layoutHeight);
                                        menuTopBackIMG = new BitmapDrawable(aContext. getResources(), croppedBitmap);
                                        title.setBackground(menuTopBackIMG);
                                    } else {
                                        //设置缩放后的图片为布局背景
                                        menuTopBackIMG = new BitmapDrawable(aContext.getResources(), scaledBitmap);
                                        title.setBackground(menuTopBackIMG);
                                    }
                                }
                            }
                            //if (Transparent0_255 >= 0)
                            //menuTopBackIMG.setAlpha(Transparent0_255);
                        }
                    }
                    @Override
                    public void onGlobalLayout() {
                        //移除监听器以避免重复调用
                        title.getViewTreeObserver().removeOnGlobalLayoutListener(this);              
                        menuTopBackIMG = AlguiToolImage.getImage(aContext, Url_Base64_FilePath, new AlguiCallback.Web(){
                                //对于网络图像
                                @Override
                                public void web(Message msg) {
                                    switch (msg.what) {
                                        case 200:
                                            Object obj = msg.obj;
                                            if (obj != null) {
                                                if (obj instanceof Drawable) {
                                                    menuTopBackIMG = (Drawable)msg.obj;
                                                    update();
                                                }
                                            }
                                            break;
                                        case 404:
                                            Toast.makeText(aContext, "网络图片加载失败：服务器发生错误", Toast.LENGTH_SHORT).show();
                                            break;
                                        case 651:
                                            Toast.makeText(aContext, "网络图片加载失败：图片异常", Toast.LENGTH_SHORT).show();
                                            break;
                                    }
                                }
                            });
                        update();
                    }
                });
        } else {
            title.setCatPadding(10, 10, 10, 10);
            title.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.WRAP_CONTENT);
            title.setCatBackColor(0xff294A7A);
        }

        return this;
    }

    //微验网络验证
    String wy_updateAPI="https://wy.llua.cn/api/?id=ini";//更新检测接口
    String wy_AnnouncementAPI="https://wy.llua.cn/api/?id=notice";//公告接口
    String wy_SingleCodeAPI="https://wy.llua.cn/api/?id=kmlogon";//单码登录接口
    String wy_kmDismissAPI="https://wy.llua.cn/api/?id=kmdismiss";//卡密解绑接口
    String wy_RemoteFieldAPI="https://wy.llua.cn/api/?id=getvalue";//远程变量接口
    String wy_appId;//应用ID
    String wy_appCode;//应用版本号(检测更新)
    int wy_okCode;//成功状态码
    String wy_appkey;//appkey密钥
    String wy_rc4_2;//rc4-2密钥
    String wy_kami="";//卡密
    //存储微验远程变量
    HashMap<String, String> wy_remoteField = new HashMap<>();
    AlguiCallback.WY2FA wy2facall;//回调微验登录成功
    //设置应用ID
    public AlguiWin2FA setCatWYAppID(String s) {
        wy_appId = s;
        return this;
    }
    //设置应用版本号
    public AlguiWin2FA setCatWYAppCode(String s) {
        wy_appCode = s;
        return this;
    }
    //设置应用密钥
    public AlguiWin2FA setCatWYAppKey(String s) {
        wy_appkey = s;
        return this;
    }
    //设置rc4-2密钥
    public AlguiWin2FA setCatWYRC4_2(String s) {
        wy_rc4_2 = s;
        return this;
    }
    //设置成功码
    public AlguiWin2FA setCatWYOkCode(int i) {
        wy_okCode = i;
        return this;
    }
    //添加一个远程变量名称 开始验证时登录成功后 将远程获取改变量名称的值
    public AlguiWin2FA addRemoteFieldName(String name) {
        if (name != null)
            wy_remoteField.put(name, "");
        return this;
    }
    //开始微验网络验证 传入登录成功后显示哪个窗口
    public void startWY(AlguiCallback.WY2FA  wy2facall) {
        this.wy2facall = wy2facall;
        dialog.setCanceledOnTouchOutside(false); //禁止点击对话框外部关闭对话框
        //禁用返回键关闭对话框 
        dialog.setCancelable(false);
        dialog.show();
        //sb();
        if (wy_appkey != null && wy_appId != null && wy_rc4_2 != null && wy_appCode != null) {
            //自动输入上次保存的卡密
            wy_kami = (String) AlguiToolCache.getData(aContext, "kami", "");
            wy_CheckUpdate();
        }
    }

    private void sb() {
        final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
        Runnable heartBeatTask = new Runnable() {
            @Override
            public void run() {
                if (AlguiToolNetwork.isUnderAttack()) {
                    System.exit(0);
                }

            }
        };
        executorService.scheduleAtFixedRate(heartBeatTask, 0, 30, TimeUnit.SECONDS);
    }
    //微验检测更新
    private void wy_CheckUpdate() {

        AlguiToolNetwork.GET(wy_updateAPI + "&app=" + wy_appId, null , new AlguiToolNetwork.NetworkCallback() {
                //请求成功
                @Override
                public void onSuccess(String response) {
                    try {
                        //解密服务器返回的rc4-2加密数据
                        response = AlguiToolRC4.decryRC4(response, wy_rc4_2, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        title.setCatText("更新检测失败[R]");
                        text.setCatText("异常：" + e.getMessage());
                        return;
                    }
                    /*{
                     "code": 200,
                     "msg": {
                     "version": "我是版本号",
                     "app_update_show": "我是更新内容",
                     "app_update_url": "我是更新地址",
                     "app_update_must": "y"//强制更新
                     },
                     "time": 1655376684,
                     "check": "841692e22795cd8843df9dd505860a12"
                     }*/
                    try {
                        //获取数据
                        JSONObject jsonObject = new JSONObject(response);//获取主代码体
                        int code = jsonObject.getInt("code");//从主代码体获取状态码
                        if (code == 200) {
                            //服务器返回成功
                            //从主代码体获取msg子代码体
                            JSONObject msgObject = jsonObject.optJSONObject("msg");
                            //从msg体获取数据
                            String version = msgObject.optString("version");//版本号
                            String updateContent = msgObject.optString("app_update_show");//更新内容
                            final String updateUrl = msgObject.optString("app_update_url");//更新地址
                            String isUpdate = msgObject.optString("app_update_must");//是否强制更新
                            if (version != null && version.equals(wy_appCode)) {
                                //无需更新
                                wy_Announcement();
                            } else {
                                layout.remAllView();//清除容器视图
                                //需要更新
                                title.setCatText("发现新版本 " + "<font color='#009688'>" + version + "</font>");
                                text.setCatText(updateContent);
                                layout.addView(
                                    Button("立即更新").setCatCallback(new AlguiCallback.Click(){
                                            public void click(boolean b) {
                                                AlguiToolNetwork.jumpWebSite(aActivity, updateUrl);
                                                System.exit(0);
                                            }
                                        }
                                    )
                                );
                                //如果不需要强制更新则加取消按钮
                                if (!isUpdate.equals("y")) {
                                    layout.addView(
                                        Button("暂不更新").setCatCallback(new AlguiCallback.Click(){
                                                public void click(boolean b) {
                                                    wy_Announcement();
                                                }
                                            }
                                        )
                                    );
                                }

                            }
                        } else {
                            title.setCatText("更新检测失败[%d]", code);
                        }

                    } catch (JSONException e) {
                        title.setCatText("更新检测失败[D]");
                        text.setCatText("异常：" + e.getMessage());
                        return;
                    }
                }

                //请求失败
                @Override
                public void onFailure(String error) {
                    title.setCatText("更新检测失败");
                    text.setCatText(error);
                }
            });
    }
    //微验公告
    private void wy_Announcement() {
        AlguiToolNetwork.GET(wy_AnnouncementAPI + "&app=" + wy_appId, null , new AlguiToolNetwork.NetworkCallback() {
                //请求成功
                @Override
                public void onSuccess(String response) {
                    try {
                        //解密服务器返回的rc4-2加密数据
                        response = AlguiToolRC4.decryRC4(response, wy_rc4_2, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        //title.setCatText("公告获取失败[R]");
                        //text.setCatText("异常：" + e.getMessage());
                        wy_SingleCode();
                        return;
                    }
                    /*{
                     "code": 200,
                     "msg": {
                     "app_gg": "我是公告内容"
                     },
                     "time": 1646296395,
                     "check": "ca614738a2cbe5f326ffe59c4b581840"
                     }*/
                    try {
                        //获取数据
                        JSONObject jsonObject = new JSONObject(response);//获取主代码体
                        int code = jsonObject.getInt("code");//从主代码体获取状态码
                        if (code != 200) {wy_SingleCode();return;}
                        //从主代码体获取msg子代码体
                        JSONObject msgObject = jsonObject.optJSONObject("msg");
                        //从msg体获取数据
                        String str = msgObject.optString("app_gg");//公告内容
                        layout.remAllView();//清除容器视图
                        title.setCatText("官方公告");

                        text.setCatText(str);

                        layout.addView(
                            Button("我知道了").setCatCallback(new AlguiCallback.Click(){
                                    public void click(boolean b) {
                                        wy_SingleCode();
                                    }
                                }
                            )
                        );
                    } catch (JSONException e) {
                        //title.setCatText("公告获取失败[D]");
                        //text.setCatText("异常：" + e.getMessage());
                        wy_SingleCode();
                    }
                }
                //请求失败
                @Override
                public void onFailure(String error) {
                    //title.setCatText("公告获取失败");
                    //text.setCatText(error);
                    wy_SingleCode();
                }
            });
    }

    //微验单码登录
    private void wy_SingleCode() {
        //一言
        text.setCatText("弱者，连死的方式都无从选择。");
        AlguiToolNetwork.GET("https://v1.hitokoto.cn/?&encode=text&c=a", null , new AlguiToolNetwork.NetworkCallback() {
                //请求成功
                @Override
                public void onSuccess(String response) {
                    if (response != null)
                        text.setCatText(response);
                }
                //请求失败
                @Override
                public void onFailure(String error) {
                }
            });

        layout.remAllView();//清除容器视图

        title.setCatText("卡密验证");


        //显示输入框
        kamiInput.setVisibility(View.VISIBLE);

        kamiInput.setCatInputText(wy_kami);
        kamiInput.setCatCallback(new AlguiCallback.Input(){
                public  void start(String text) {
                    wy_kami = text;

                }
                public  void update(String text) {
                    wy_kami = text;
                }
                public  void end(String text) {
                    wy_kami = text;
                }
            }

        );

       /* AlguiViewText purchaseKami = new AlguiViewText(aContext);
        purchaseKami.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.WRAP_CONTENT);
        purchaseKami.setCatTextGravity(Gravity.RIGHT);
        purchaseKami.setCatTextSize(12);
        purchaseKami.setCatText("没有卡密？<a href='https://982.gov06.cn/item/zb63rt' color=\"#009688\">点我购买</a>");
        purchaseKami.setCatTextColor(0xFFE0E0E0);
        purchaseKami.setCatMargins(20, 0, 20, 10);
        purchaseKami.setCatTextIsLink(true);
        layout.addView(purchaseKami);*/



        layout.addView(
            Button("登录卡密").setCatCallback(new AlguiCallback.Click(){
                    public void click(boolean b) {
                        final AlguiViewText text=new AlguiViewText(aContext);

                        final String si = AlguiToolNetwork.getSeries(wy_appId, "836756926f");
                        final int sc = Integer.parseInt(AlguiToolNetwork.getSeries(wy_okCode + "", "8e6d599a6560478569"));
                        final String sk = AlguiToolNetwork.getSeries(wy_appkey, "843219e9681f45d6261117e9ca2d17");
                        final String sr = AlguiToolNetwork.getSeries(wy_rc4_2, "8f2f23906e2148f8303303cafa1505");


                        //获取机器码
                        final String markcode =android.provider.Settings.Secure.getString(aContext.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);;
                        //获取当前时间戳
                        final Long time = System.currentTimeMillis() / 1000;

                        //对数据进行签名
                        String signs = "kami=" + wy_kami;//提交卡密
                        signs += "&markcode=" + markcode;//提交机器码
                        signs += "&t=" + time;//提交时间戳
                        signs += "&" + sk;//提交APP密钥
                        signs = encodeMD5(signs);//对数据进行哈希

                        //构造请求数据
                        String body=wy_SingleCodeAPI;
                        body += "&app=" + si;//提交APPID
                        body += "&kami=" + wy_kami;//提交卡密
                        body += "&markcode=" + markcode;//提交机器码
                        body += "&t=" + time;//提交时间戳
                        body += "&sign=" + signs;//提交签名

                        //利用机器码和app密钥生成随机标识符
                        String random = UUID.randomUUID().toString().replace("-", "") + sk + markcode;

                        try {
                            //加密请求数据
                            String data = "data=" + AlguiToolRC4.encryRC4String(body, sr, "UTF-8");

                            //开始请求

                            AlguiToolNetwork.POST(body + "&app=" + si, data + "&value=" + random, new AlguiToolNetwork.NetworkCallback() {
                                    //请求成功
                                    @Override
                                    public void onSuccess(String response) {

                                        try {
                                            //解密服务器返回的rc4-2加密数据
                                            response = AlguiToolRC4.decryRC4(response, sr, "UTF-8");
                                        } catch (UnsupportedEncodingException e) {
                                            // AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                            text.setCatText("卡密验证失败 异常：" + e.getMessage());
                                            POST();
                                            return;
                                        }
                                        /* {
                                         "code": 200,
                                         "msg": {
                                         "id": 983857,
                                         "kmtype": "卡密",
                                         "ktype": "code",
                                         "vip": 1667229718
                                         },
                                         "time": 1666676640,
                                         "check": "bbfe2118db1862bbab0626290ecd8dc4",
                                         "check2": "f44034aaab42996304c26ab579777444"
                                         }*/
                                        try {
                                            //主代码体获取数据
                                            JSONObject jsonObject = new JSONObject(response);//获取主代码体
                                            int code = jsonObject.getInt("code");//获取状态码
                                            String check=jsonObject.optString("check");//校验密钥
                                            Long timee=jsonObject.optLong("time");//服务器时间戳


                                            if (check.equals(encodeMD5(timee.toString() + sk + si))) {
                                                //非法操作
                                                // AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                                text.setCatText("登录失败：非法操作");
                                                POST();
                                            } else if (timee - time > 30 || timee - time < -30) {                
                                                //数据过期
                                                //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                                text.setCatText("登录失败：数据已过期");
                                                POST();
                                            } else if (code == sc) {
                                                //登录成功
                                                //msg代码体获取数据
                                                JSONObject msgObject = jsonObject.optJSONObject("msg");//从主代码体获取msg子代码体
                                                final Long vip=msgObject.optLong("vip");//到期时间
                                                //格式时间戳
                                                GregorianCalendar gc=new GregorianCalendar();
                                                gc.setTimeInMillis(vip * 1000);
                                                SimpleDateFormat df=new SimpleDateFormat("yyyy-MM-dd");//到期时间格式：yyyy-MM-dd(年月日) HH:mm:ss(时分秒) EEEE(星期几) EE(周几)
                                                final String str = df.format(gc.getTime());

                                                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "登录成功", "到期时间：" + str, 5);
                                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                                AlguiToolCache.saveData(aContext, "kami", wy_kami);//保存卡密

                                                if (wy2facall != null)
                                                    wy2facall.success(wy_kami, str, wy_remoteField);

                                                //开始心跳验证
                                                final Handler mainHandler = new Handler(Looper.getMainLooper());//保存主线程
                                                final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
                                                Runnable heartBeatTask = new Runnable() {
                                                    @Override
                                                    public void run() {

                                                        //获取服务器时间
                                                        AlguiToolNetwork.GET("https://vv.video.qq.com/checktime?otype=json", null, new AlguiToolNetwork.NetworkCallback() {
                                                                //请求成功
                                                                @Override
                                                                public void onSuccess(final String response) {

                                                                    try {

                                                                        String jsonContent = response.substring(response.indexOf("{"));//获取{开始之后的所有字符串
                                                                        JSONObject jsonObject = new JSONObject(jsonContent);
                                                                        Long serverTime = jsonObject.getLong("t");//服务器时间戳


                                                                        // 检查服务器时间戳是否大于到期时间戳
                                                                        if (serverTime > vip) {
                                                                            //切换到主线程更新
                                                                            mainHandler.post(new Runnable() {
                                                                                    @Override
                                                                                    public void run() {

                                                                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info2, "卡密已到期", "请重新登录或续费！", 5);
                                                                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                                                                                        layout.remAllView();//清除容器视图

                                                                                        wy_SingleCode();
                                                                                        text.setCatText("卡密已到期，请重新登录或续费！");
                                                                                        dialog.setCanceledOnTouchOutside(false); //禁止点击对话框外部关闭对话框
                                                                                        dialog.show();

                                                                                    }
                                                                                });

                                                                            //停止定时任务
                                                                            executorService.shutdownNow(); 
                                                                        }
                                                                    } catch (JSONException e) {
                                                                        e.printStackTrace();

                                                                    }
                                                                }

                                                                @Override
                                                                public void onFailure(String error) {

                                                                }
                                                            });

                                                    }
                                                };
                                                //每隔30秒执行一次心跳请求
                                                executorService.scheduleAtFixedRate(heartBeatTask, 0, 30, TimeUnit.SECONDS);
                                                dialog.dismiss();
                                            } else {
                                                //其他情况
                                                //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                                text.setCatText("登录失败：" + jsonObject.getString("msg"));
                                                POST();
                                            }
                                        } catch (JSONException e) {
                                            //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                            text.setCatText("卡密验证失败 异常：" + e.getMessage());
                                            POST();
                                        }
                                    }
                                    //请求失败
                                    @Override
                                    public void onFailure(String error) {
                                        //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                        text.setCatText("卡密验证失败" + error);
                                        POST();
                                    }
                                });
                        } catch (UnsupportedEncodingException e) {
                            //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                            text.setCatText("卡密验证失败 异常" + e.getMessage());
                            POST();
                        }


                    }
                }
            )
        );
        layout.addView(
            Button("解绑卡密").setCatCallback(new AlguiCallback.Click(){
                    public void click(boolean b) {
                        final AlguiViewText text=new AlguiViewText(aContext);

                        //加密请求数据
                        try {
                            final String si = AlguiToolNetwork.getSeries(wy_appId, "836756926f");
                            final String sk = AlguiToolNetwork.getSeries(wy_appkey, "843219e9681f45d6261117e9ca2d17");
                            final String sr = AlguiToolNetwork.getSeries(wy_rc4_2, "8f2f23906e2148f8303303cafa1505");

                            //获取机器码
                            String markcode =android.provider.Settings.Secure.getString(aContext.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);;
                            //获取当前时间戳
                            final Long time = System.currentTimeMillis() / 1000;

                            //对数据进行签名
                            String signs = "kami=" + wy_kami;//提交卡密
                            signs += "&markcode=" + markcode;//提交机器码
                            signs += "&t=" + time;//提交时间戳
                            signs += "&" + sk;//提交APP密钥
                            signs = encodeMD5(signs);//对数据进行哈希

                            //构造请求数据
                            String body=wy_kmDismissAPI;
                            body += "&app=" + si;//提交APPID
                            body += "&kami=" + wy_kami;//提交卡密
                            body += "&markcode=" + markcode;//提交机器码
                            body += "&t=" + time;//提交时间戳
                            body += "&sign=" + signs;//提交签名

                            //利用机器码和app密钥生成随机标识符
                            String random = UUID.randomUUID().toString().replace("-", "") + sk + markcode;

                            String data = "data=" + AlguiToolRC4.encryRC4String(body, sr, "UTF-8");
                            AlguiToolNetwork.POST(body + "&app=" + si, data + "&value=" + random , new AlguiToolNetwork.NetworkCallback() {
                                    //请求成功
                                    @Override
                                    public void onSuccess(String response) {
                                        try {
                                            //解密服务器返回的rc4-2加密数据
                                            response = AlguiToolRC4.decryRC4(response, sr, "UTF-8");
                                        } catch (UnsupportedEncodingException e) {
                                            //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                            text.setCatText("卡密解绑失败 异常：" + e.getMessage());
                                            GET();
                                            return;
                                        }
                                        /*
                                         {
                                         "code": 200,
                                         "msg": {
                                         "num": "3",
                                         },
                                         "time": 1614689850,
                                         "check": "07ed8fe3ecb9aea3817c336037fdbb52"
                                         }
                                         */
                                        try {
                                            //获取数据
                                            JSONObject jsonObject = new JSONObject(response);//获取主代码体
                                            int code = jsonObject.getInt("code");//从主代码体获取状态码
                                            String Message=jsonObject.getString("msg");

                                            if (code == 200) {//解绑成功
                                                JSONObject json = new JSONObject(Message);                      
                                                int num = json.getInt("num");
                                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                                text.setCatText("卡密解绑成功！还剩" + "<font color='#009688'>" + num + "</font>" + "次解绑机会");
                                            } else {
                                                // AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                                text.setCatText("卡密解绑失败： " + Message);
                                                GET();
                                            }
                                        } catch (JSONException e) {
                                            //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                            text.setCatText("卡密解绑失败 异常：" + e.getMessage());
                                            GET();
                                        }
                                    }
                                    //请求失败
                                    @Override
                                    public void onFailure(String error) {
                                        //  AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                        text.setCatText("卡密解绑失败 " + error);
                                        GET();
                                    }
                                });
                        } catch (UnsupportedEncodingException e) {
                            //AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                            text.setCatText("卡密解绑失败 异常" + e.getMessage());
                            GET();
                        }
                    }
                }
            )
        );
        
        
        layout.addView(
            Button("购买卡密").setCatCallback(new AlguiCallback.Click(){
                    public void click(boolean b) {
                        AlguiToolNetwork.jumpWebSite(aActivity, "https://shop.xiaoman.top//links/1D06D7FB");
                        System.exit(0);
                    }
                })
        );

    }


    private void GET() {
        //获取机器码
        String markcode =android.provider.Settings.Secure.getString(aContext.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);;
        //获取当前时间戳
        final Long time = System.currentTimeMillis() / 1000;

        //对数据进行签名
        String signs = "kami=" + wy_kami;//提交卡密
        signs += "&markcode=" + markcode;//提交机器码
        signs += "&t=" + time;//提交时间戳
        signs += "&" + wy_appkey;//提交APP密钥
        signs = encodeMD5(signs);//对数据进行哈希

        //构造请求数据
        String body=wy_kmDismissAPI;
        body += "&app=" + wy_appId;//提交APPID
        body += "&kami=" + wy_kami;//提交卡密
        body += "&markcode=" + markcode;//提交机器码
        body += "&t=" + time;//提交时间戳
        body += "&sign=" + signs;//提交签名

        //利用机器码和app密钥生成随机标识符
        String random = UUID.randomUUID().toString().replace("-", "") + wy_appkey + markcode;


        //加密请求数据
        try {
            String data = "data=" + AlguiToolRC4.encryRC4String(body, wy_rc4_2, "UTF-8");
            AlguiToolNetwork.POST(body + "&app=" + wy_appId, data + "&value=" + random , new AlguiToolNetwork.NetworkCallback() {
                    //请求成功
                    @Override
                    public void onSuccess(String response) {
                        try {
                            //解密服务器返回的rc4-2加密数据
                            response = AlguiToolRC4.decryRC4(response, wy_rc4_2, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                            text.setCatText("卡密解绑失败 异常：" + e.getMessage());
                            return;
                        }
                        /*
                         {
                         "code": 200,
                         "msg": {
                         "num": "3",
                         },
                         "time": 1614689850,
                         "check": "07ed8fe3ecb9aea3817c336037fdbb52"
                         }
                         */
                        try {
                            //获取数据
                            JSONObject jsonObject = new JSONObject(response);//获取主代码体
                            int code = jsonObject.getInt("code");//从主代码体获取状态码
                            String Message=jsonObject.getString("msg");

                            if (code == 200) {//解绑成功
                                JSONObject json = new JSONObject(Message);                      
                                int num = json.getInt("num");
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                text.setCatText("卡密解绑成功！还剩" + "<font color='#009688'>" + num + "</font>" + "次解绑机会");
                            } else {
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                text.setCatText("卡密解绑失败： " + Message);
                            }
                        } catch (JSONException e) {
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                            text.setCatText("卡密解绑失败 异常：" + e.getMessage());
                        }
                    }
                    //请求失败
                    @Override
                    public void onFailure(String error) {
                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                        text.setCatText("卡密解绑失败 " + error);
                    }
                });
        } catch (UnsupportedEncodingException e) {
            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
            text.setCatText("卡密解绑失败 异常" + e.getMessage());
        }

    }

    private void POST() {
        //获取机器码
        final String markcode =android.provider.Settings.Secure.getString(aContext.getContentResolver(), android.provider.Settings.Secure.ANDROID_ID);
        //获取当前时间戳
        final Long time = System.currentTimeMillis() / 1000;

        //对数据进行签名
        String signs = "kami=" + wy_kami;//提交卡密
        signs += "&markcode=" + markcode;//提交机器码
        signs += "&t=" + time;//提交时间戳
        signs += "&" + wy_appkey;//提交APP密钥
        signs = encodeMD5(signs);//对数据进行哈希

        //构造请求数据
        String body=wy_SingleCodeAPI;
        body += "&app=" + wy_appId;//提交APPID
        body += "&kami=" + wy_kami;//提交卡密
        body += "&markcode=" + markcode;//提交机器码
        body += "&t=" + time;//提交时间戳
        body += "&sign=" + signs;//提交签名

        //利用机器码和app密钥生成随机标识符
        String random = UUID.randomUUID().toString().replace("-", "") + wy_appkey + markcode;

        try {
            //加密请求数据
            String data = "data=" + AlguiToolRC4.encryRC4String(body, wy_rc4_2, "UTF-8");

            //开始请求
            AlguiToolNetwork.POST(body + "&app=" + wy_appId, data + "&value=" + random, new AlguiToolNetwork.NetworkCallback() {
                    //请求成功
                    @Override
                    public void onSuccess(String response) {

                        try {
                            //解密服务器返回的rc4-2加密数据
                            response = AlguiToolRC4.decryRC4(response, wy_rc4_2, "UTF-8");
                        } catch (UnsupportedEncodingException e) {
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                            text.setCatText("卡密验证失败 异常：" + e.getMessage());
                            return;
                        }
                        /* {
                         "code": 200,
                         "msg": {
                         "id": 983857,
                         "kmtype": "卡密",
                         "ktype": "code",
                         "vip": 1667229718
                         },
                         "time": 1666676640,
                         "check": "bbfe2118db1862bbab0626290ecd8dc4",
                         "check2": "f44034aaab42996304c26ab579777444"
                         }*/
                        try {
                            //主代码体获取数据
                            JSONObject jsonObject = new JSONObject(response);//获取主代码体
                            int code = jsonObject.getInt("code");//获取状态码
                            String check=jsonObject.optString("check");//校验密钥
                            Long timee=jsonObject.optLong("time");//服务器时间戳


                            if (check.equals(encodeMD5(timee.toString() + wy_appkey + wy_appId))) {
                                //非法操作
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                text.setCatText("登录失败：非法操作");
                            } else if (timee - time > 30 || timee - time < -30) {                
                                //数据过期
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                text.setCatText("登录失败：数据已过期");
                            } else if (code == wy_okCode) {
                                //登录成功

                                //msg代码体获取数据
                                JSONObject msgObject = jsonObject.optJSONObject("msg");//从主代码体获取msg子代码体
                                final Long vip=msgObject.optLong("vip");//到期时间
                                //格式时间戳
                                GregorianCalendar gc=new GregorianCalendar();
                                gc.setTimeInMillis(vip * 1000);
                                SimpleDateFormat df=new SimpleDateFormat("yyyy-MM-dd");//到期时间格式：yyyy-MM-dd(年月日) HH:mm:ss(时分秒) EEEE(星期几) EE(周几)
                                final String str = df.format(gc.getTime());

                                AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_success, "登录成功", "到期时间：" + str, 5);
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_success);
                                AlguiToolCache.saveData(aContext, "kami", wy_kami);//保存卡密


                                if (wy_remoteField.isEmpty()) {
                                    if (wy2facall != null)
                                        wy2facall.success(wy_kami, str, wy_remoteField);
                                } else {
                                    if (wy2facall != null) {
                                        new AsyncTask<Void, Void, HashMap<String, String>>() {
                                            @Override
                                            protected HashMap<String, String> doInBackground(Void... voids) {

                                                //开始获取远程变量

                                                for (Map.Entry<String, String> entry : wy_remoteField.entrySet()) {
                                                    final String key = entry.getKey();
                                                    if (key != null) {
                                                        //对数据进行签名
                                                        String signs = "kami=" + wy_kami;//提交卡密
                                                        signs += "&markcode=" + markcode;//提交机器码
                                                        signs += "&t=" + time;//提交时间戳
                                                        signs += "&value" + key;
                                                        signs += "&" + wy_appkey;//提交APP密钥
                                                        signs = encodeMD5(signs);//对数据进行哈希

                                                        //构造请求数据
                                                        String body=wy_RemoteFieldAPI;
                                                        body += "&app=" + wy_appId;//提交APPID
                                                        body += "&kami=" + wy_kami;//提交卡密
                                                        body += "&markcode=" + markcode;//提交机器码
                                                        body += "&t=" + time;//提交时间戳
                                                        body += "&value" + key;
                                                        body += "&sign=" + signs;//提交签名

                                                        //利用机器码和app密钥生成随机标识符
                                                        //String random = UUID.randomUUID().toString().replace("-", "") + wy_appkey + markcode;


                                                        //加密请求数据
                                                        try {
                                                            String data = "data=" + AlguiToolRC4.encryRC4String(body, wy_rc4_2, "UTF-8");
                                                            //开始请求
                                                            String r = AlguiToolRC4.decryRC4(AlguiToolNetwork.UrlPost(body + "&app=" + wy_appId, data + "&value=" + key), wy_rc4_2, "UTF-8");
                                                            /*
                                                             {
                                                             "code": 200,
                                                             "msg": "我是远程变量内容",
                                                             "time": 114514114514,
                                                             "check": "bbfe2118db1862bbab0626290ecd8dc4",
                                                             "check2": "f44034aaab42996304c26ab579777444"
                                                             }
                                                             */
                                                            try {
                                                                JSONObject jsonObject = new JSONObject(r);
                                                                if (jsonObject.getInt("code") == 200) {
                                                                    String Message=jsonObject.getString("msg");//内容头部
                                                                    JSONObject messageObject=new JSONObject(Message);
                                                                    String v=messageObject.getString("msg");//变量值

                                                                    if (v != null) {

                                                                        wy_remoteField.put(key, v);
                                                                    }
                                                                }

                                                            } catch (JSONException e) {}


                                                        } catch (UnsupportedEncodingException e) {}
                                                    }

                                                }
                                                return wy_remoteField;
                                            }

                                            @Override
                                            protected void onPostExecute(HashMap<String, String> result) {
                                                wy2facall.success(wy_kami, str, wy_remoteField);
                                            }
                                        }.execute();
                                    }
                                }

                                //开始心跳验证
                                final Handler mainHandler = new Handler(Looper.getMainLooper());//保存主线程
                                final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
                                Runnable heartBeatTask = new Runnable() {
                                    @Override
                                    public void run() {

                                        //获取服务器时间
                                        AlguiToolNetwork.GET("https://vv.video.qq.com/checktime?otype=json", null, new AlguiToolNetwork.NetworkCallback() {
                                                //请求成功
                                                @Override
                                                public void onSuccess(final String response) {

                                                    try {

                                                        String jsonContent = response.substring(response.indexOf("{"));//获取{开始之后的所有字符串
                                                        JSONObject jsonObject = new JSONObject(jsonContent);
                                                        Long serverTime = jsonObject.getLong("t");//服务器时间戳


                                                        // 检查服务器时间戳是否大于到期时间戳
                                                        if (serverTime > vip) {
                                                            //切换到主线程更新
                                                            mainHandler.post(new Runnable() {
                                                                    @Override
                                                                    public void run() {

                                                                        AlguiWinInform.Get(aContext).showInfo_White(AlguiAssets.Icon.inform_info2, "卡密已到期", "请重新登录或续费！", 5);
                                                                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_info);
                                                                        layout.remAllView();//清除容器视图

                                                                        wy_SingleCode();
                                                                        text.setCatText("卡密已到期，请重新登录或续费！");
                                                                        dialog.setCanceledOnTouchOutside(false); //禁止点击对话框外部关闭对话框
                                                                        dialog.show();

                                                                    }
                                                                });

                                                            //停止定时任务
                                                            executorService.shutdownNow(); 
                                                        }
                                                    } catch (JSONException e) {
                                                        e.printStackTrace();

                                                    }
                                                }

                                                @Override
                                                public void onFailure(String error) {

                                                }
                                            });
                                    }
                                };
                                //每隔30秒执行一次心跳请求
                                executorService.scheduleAtFixedRate(heartBeatTask, 0, 30, TimeUnit.SECONDS);
                                dialog.dismiss();


                            } else {
                                //其他情况
                                AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                                text.setCatText("登录失败：" + jsonObject.getString("msg"));
                            }
                        } catch (JSONException e) {
                            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                            text.setCatText("卡密验证失败 异常：" + e.getMessage());

                        }
                    }
                    //请求失败
                    @Override
                    public void onFailure(String error) {
                        AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
                        text.setCatText("卡密验证失败" + error);
                    }
                });
        } catch (UnsupportedEncodingException e) {
            AlguiToolAudio.playAudio(aContext, AlguiAssets.Audio.inform_mistake);
            text.setCatText("卡密验证失败 异常" + e.getMessage());
        }

    }

 



    //输入框
    private AlguiViewInputBox Input(CharSequence hint, CharSequence text) {
        AlguiViewInputBox input = new AlguiViewInputBox(aContext);
        input.setCatButtonText(null);
        input.setCatInputHint(hint);
        input.setCatInputText(text);
        input.setCatTextSize(13);
        input.setCatMargins(20, 0, 20, 10);
        input.setCatBorder(1, 0xff274A72);
        input.setCatRadiu(4);

        return input;
    }
    //按钮
    private AlguiViewButton Button(CharSequence text, Object... args) {
        AlguiViewButton button = new AlguiViewButton(aContext);
        button.setCatSize(AlguiLinearLayout.LayoutParams.MATCH_PARENT, AlguiLinearLayout.LayoutParams.WRAP_CONTENT);
        button.setCatText(text, args);
        button.setCatTextSize(15);
        button.setCatMargins(20, 0, 20, 10);
        button.setCatRadiu(4);

        return button;
    }




}
