package com.bytecat.algui;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;

/**
 * <AUTHOR> - ［Copyright © 2024 𝗕𝘆𝘁𝗲𝗖𝗮𝘁 版权所有］游戏逆向交流群730967224 - 作者QQ3353484607
 * @Date 2024/09/16 03:33
 * @Describe Algui全局悬浮窗后台服务 用于全局显示悬浮窗
 */
public class AlguiService extends Service {

    public static final String TAG = "AlguiService";

    public Context context;

    //用于绑定AlguiService组件和其他组件之间的交互 返回null表示不支持绑定
    @Override
    public IBinder onBind(Intent Intent) {
        return null;
    }
    //创建后台服务
    @Override
    public void onCreate() {
        super.onCreate();
        context = getApplicationContext();
        Main.start(context);
    }

    //后台服务销毁
    @Override
    public void onDestroy() {
        super.onDestroy();
    }

}
