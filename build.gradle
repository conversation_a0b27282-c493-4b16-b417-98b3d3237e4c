buildscript {
    ext.build_gradle_version = '8.1.0'

    repositories {
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:$build_gradle_version"
    }
}

plugins {
    id 'com.android.application' version '8.1.0' apply false
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public/' } 
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin/' }
        google()
        mavenCentral()
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}










